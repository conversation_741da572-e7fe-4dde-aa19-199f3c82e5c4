"use client";
import SidebarCreateProject from "@/components/CreateProject/SidebarCreateProject";
import React, { useEffect, useRef, Suspense } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { ToastProvider } from "@/lib/ToastProvider";
import { useEditProject, useEditNavigation } from "@/hooks/useEditProject";
import { projectAPI } from "@/services/projectService";

// Component that uses useSearchParams - needs to be wrapped in Suspense
function LayoutContent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const {
    projectInfo,
    searchEngineConfigs,
    keywords,
    canAccessStep,
    stepCompletion,
    currentStep,
    loadExistingProject,
  } = useCreateProjectStore();
  const { isEditMode, isLoadingProject, projectError } = useEditProject();
  const { urls } = useEditNavigation();

  // If arriving via OAuth callback with pid, fetch project and hydrate store like edit mode
  useEffect(() => {
    const pid = searchParams?.get("pid");
    if (!pid) return;

    // If we're already in edit mode for this project, no need to fetch
    if (isEditMode && projectInfo?.id === pid) return;

    // If store has different project or none, fetch and load
    (async () => {
      try {
        const resp = await projectAPI.getProject(pid);
        await loadExistingProject(resp.data);
      } catch (e) {
        console.warn("Failed to hydrate project from pid:", e);
      }
    })();
  }, [searchParams, isEditMode, projectInfo?.id, loadExistingProject]);

  // Simple fixed sidebar positioning
  useEffect(() => {
    const sidebar = sidebarRef.current;
    if (!sidebar) return;

    // Apply fixed positioning with proper CSS classes
    sidebar.style.position = 'fixed';
    sidebar.style.top = '0';
    sidebar.style.left = '0';
    sidebar.style.height = '100vh';
    sidebar.style.zIndex = '40';
    sidebar.style.overflowY = 'auto';
    sidebar.style.paddingTop = '24px'; // Reduced top padding
    sidebar.style.paddingLeft = '24px'; // px-6 equivalent
    sidebar.style.paddingRight = '24px';
    sidebar.style.paddingBottom = '24px';

    return () => {
      // Clean up styles on unmount
      sidebar.style.position = '';
      sidebar.style.top = '';
      sidebar.style.left = '';
      sidebar.style.height = '';
      sidebar.style.zIndex = '';
      sidebar.style.overflowY = '';
      sidebar.style.paddingTop = '';
      sidebar.style.paddingLeft = '';
      sidebar.style.paddingRight = '';
      sidebar.style.paddingBottom = '';
    };
  }, []);

  useEffect(() => {
    // Skip navigation validation entirely in edit mode or while loading project data
    if (isEditMode || isLoadingProject) {
      return;
    }

    // Always allow access to Analytics Services step (OAuth return landing)
    // This page must not be blocked by step guards in create mode
    if (pathname && pathname.includes("/create-project/analytics-services")) {
      return;
    }

    // Enhanced navigation validation using the store's canAccessStep method
    const isStepAccessible = (stepUrl: string): boolean => {
      // Use the store's enhanced validation logic
      return canAccessStep(stepUrl);
    };

    // Only perform navigation validation in create mode (excluding analytics-services)
    if (pathname && !isStepAccessible(pathname)) {
      // Use enhanced redirect logic based on step completion with proper URL building
      if (!canAccessStep("/create-project/search-engines")) {
        router.replace(urls.projectInformation);
      } else if (!canAccessStep("/create-project/keywords")) {
        router.replace(urls.searchEngines);
      } else if (!canAccessStep("/create-project/competitors")) {
        router.replace(urls.keywords);
      } else if (!canAccessStep("/create-project/analytics-services")) {
        router.replace(urls.competitors);
      }
    }
  }, [
    pathname,
    projectInfo,
    searchEngineConfigs,
    keywords,
    router,
    isEditMode,
    isLoadingProject,
    canAccessStep,
    urls,
    stepCompletion,
    currentStep,
  ]);

  // Show loading state while fetching project data in edit mode
  if (isEditMode && isLoadingProject) {
    return <LayoutLoading />;
  }

  // Show error state if project failed to load
  if (isEditMode && projectError) {
    return (
      <div className="w-full max-w-8xl mx-auto px-4 xl:px-2 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8 flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="text-red-600 text-lg font-semibold">
            Failed to load project data
          </div>
          <div className="text-gray-600">
            Please try refreshing the page or go back to the projects list.
          </div>
          <button
            onClick={() => router.push("/dashboard/my-projects")}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            Back to Projects
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F4F4F4]">
      {/* Fixed sidebar - positioned fixed on the left */}
      <div
        ref={sidebarRef}
        className="hidden lg:block lg:w-80 xl:w-96 bg-[#F4F4F4]"
      >
        <SidebarCreateProject />
      </div>

      {/* Main content area with left margin to account for fixed sidebar */}
      <div className="lg:ml-80 xl:ml-96">
        <div className="w-full max-w-6xl mx-auto px-4 xl:px-6 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8">
          {children}
        </div>
      </div>
      <ToastProvider />
    </div>
  );
}

// Loading fallback component
function LayoutLoading() {
  const skeletonSidebarRef = useRef<HTMLDivElement>(null);

  // Apply same positioning logic as the actual sidebar
  useEffect(() => {
    const sidebar = skeletonSidebarRef.current;
    if (!sidebar) return;

    // Apply fixed positioning with proper CSS classes
    sidebar.style.position = 'fixed';
    sidebar.style.top = '0';
    sidebar.style.left = '0';
    sidebar.style.height = '100vh';
    sidebar.style.zIndex = '40';
    sidebar.style.overflowY = 'auto';
    sidebar.style.paddingTop = '24px'; // Reduced top padding
    sidebar.style.paddingLeft = '24px'; // px-6 equivalent
    sidebar.style.paddingRight = '24px';
    sidebar.style.paddingBottom = '24px';

    return () => {
      // Clean up styles on unmount
      sidebar.style.position = '';
      sidebar.style.top = '';
      sidebar.style.left = '';
      sidebar.style.height = '';
      sidebar.style.zIndex = '';
      sidebar.style.overflowY = '';
      sidebar.style.paddingTop = '';
      sidebar.style.paddingLeft = '';
      sidebar.style.paddingRight = '';
      sidebar.style.paddingBottom = '';
    };
  }, []);

  return (
    <>
      {/* Fixed sidebar loading skeleton */}
      <div
        ref={skeletonSidebarRef}
        className="hidden lg:block lg:w-80 xl:w-96 bg-[#F4F4F4]"
      >
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 space-y-6">
          {/* Logo skeleton */}
          <div className="flex justify-center">
            <div className="animate-pulse bg-gray-200 rounded-lg h-12 w-32"></div>
          </div>

          {/* Project name skeleton */}
          <div className="text-center space-y-2">
            <div className="animate-pulse bg-gray-200 rounded h-5 w-24 mx-auto"></div>
            <div className="animate-pulse bg-gray-200 rounded h-4 w-20 mx-auto"></div>
          </div>

          {/* Navigation steps skeleton */}
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="flex items-center gap-3 p-3 rounded-xl">
                <div className="animate-pulse bg-gray-200 rounded-lg h-8 w-8"></div>
                <div className="flex-1 space-y-1">
                  <div className="animate-pulse bg-gray-200 rounded h-4 w-full"></div>
                  <div className="animate-pulse bg-gray-200 rounded h-3 w-16"></div>
                </div>
              </div>
            ))}
          </div>

          {/* Progress skeleton */}
          <div className="pt-6 border-t border-gray-100 space-y-2">
            <div className="flex justify-between items-center">
              <div className="animate-pulse bg-gray-200 rounded h-3 w-12"></div>
              <div className="animate-pulse bg-gray-200 rounded h-3 w-8"></div>
            </div>
            <div className="w-full bg-gray-100 rounded-full h-2">
              <div className="animate-pulse bg-gray-200 h-2 rounded-full w-3/5"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area with left margin to account for fixed sidebar */}
      <div className="lg:ml-80 xl:ml-96 min-h-screen">
        <div className="w-full max-w-6xl mx-auto px-4 xl:px-6 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 space-y-6">
            {/* Header skeleton */}
            <div className="space-y-4">
              <div className="animate-pulse bg-gray-200 rounded h-8 w-64"></div>
              <div className="animate-pulse bg-gray-200 rounded h-4 w-96"></div>
            </div>

            {/* Form sections skeleton */}
            <div className="space-y-8">
              {/* Section 1 */}
              <div className="space-y-4">
                <div className="animate-pulse bg-gray-200 rounded h-6 w-48"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="animate-pulse bg-gray-100 rounded-lg h-12"></div>
                  <div className="animate-pulse bg-gray-100 rounded-lg h-12"></div>
                </div>
              </div>

              {/* Section 2 */}
              <div className="space-y-4">
                <div className="animate-pulse bg-gray-200 rounded h-6 w-40"></div>
                <div className="animate-pulse bg-gray-100 rounded-lg h-32"></div>
              </div>

              {/* Section 3 */}
              <div className="space-y-4">
                <div className="animate-pulse bg-gray-200 rounded h-6 w-52"></div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="animate-pulse bg-gray-100 rounded-lg h-20"></div>
                  <div className="animate-pulse bg-gray-100 rounded-lg h-20"></div>
                  <div className="animate-pulse bg-gray-100 rounded-lg h-20"></div>
                </div>
              </div>
            </div>

            {/* Action buttons skeleton */}
            <div className="flex justify-between pt-6">
              <div className="animate-pulse bg-gray-200 rounded-lg h-10 w-20"></div>
              <div className="animate-pulse bg-primary/20 rounded-lg h-10 w-24"></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={<LayoutLoading />}>
      <LayoutContent>{children}</LayoutContent>
    </Suspense>
  );
}
